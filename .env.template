# OpenAI Configuration (REQUIRED)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-actual-openai-api-key-here

# LLM Configuration (optional - will auto-detect based on API key)
# LLM_PROVIDER=openai  # Auto-detected when valid OpenAI API key is provided
# LLM_MODEL=gpt-4o-mini  # Default for OpenAI

# Application Configuration (optional)
APP_NAME=intake_t
APP_ENV=development
HOST=127.0.0.1
PORT=8000
RELOAD=true

# Client Configuration (optional)
CLIENT_CONFIG_PATH=client_config.json

# Database Configuration (optional - will use SQLite by default)
# DATABASE_URL=sqlite:///./triage.db

# Health Data Encryption Key (optional - will generate if not provided)
# HEALTH_DATA_ENCRYPTION_KEY=your_32_byte_base64_encoded_key_here

# For local LLM (if you want to switch back)
# LLM_PROVIDER=local
# LLM_BASE_URL=http://127.0.0.1:1234/v1